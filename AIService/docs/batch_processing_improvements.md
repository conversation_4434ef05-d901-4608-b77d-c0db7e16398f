# Batch Processing Improvements for Qdrant Managers

## Overview

This document describes the improvements made to both `qdrant_manager.py` and `sam_qdrant_manager.py` to handle large datasets more efficiently and prevent connection issues with Qdrant server.

## Problem Statement

The original scripts had the following issues when processing large numbers of opportunities:

1. **Sequential Processing**: All opportunities were processed one after another without batching
2. **Connection Overload**: Large datasets could overwhelm the Qdrant server causing "connection refused" errors
3. **Memory Issues**: Processing thousands of opportunities could lead to memory exhaustion
4. **No Retry Logic**: Temporary connection failures would cause the entire process to fail
5. **Poor Resource Management**: No delays or cleanup between operations

## Improvements Implemented

### 1. Batch Processing

**Before:**
```python
for i, opp in enumerate(opportunities, 1):
    # Process each opportunity immediately
    process_opportunity(opp)
```

**After:**
```python
# Process opportunities in configurable batches
batch_size = 500  # Default, configurable via --batch-size
total_batches = (len(opportunities) + batch_size - 1) // batch_size

for batch_num in range(total_batches):
    batch_opportunities = opportunities[start_idx:end_idx]
    # Process batch with logging and error handling
```

**Benefits:**
- Reduces server load by processing in smaller chunks
- Better progress tracking with batch-level reporting
- Easier to resume from failures
- Configurable batch size (default: 500)

### 2. Enhanced Connection Management

**Before:**
```python
self.qdrant_client = QdrantClient(url=settings.qdrant_url)
```

**After:**
```python
self.qdrant_client = QdrantClient(
    url=settings.qdrant_url,
    timeout=settings.qdrant_timeout,
    prefer_grpc=settings.qdrant_prefer_grpc
)
```

**Benefits:**
- Uses configured timeout settings
- Leverages gRPC for better performance when available
- More robust connection handling

### 3. Retry Logic with Exponential Backoff

**New Feature:**
```python
async def retry_with_backoff(self, func, *args, max_retries: int = 3, base_delay: float = 1.0, **kwargs):
    for attempt in range(max_retries):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            delay = base_delay * (2 ** attempt)
            logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay} seconds...")
            await asyncio.sleep(delay)
```

**Benefits:**
- Handles temporary connection failures gracefully
- Exponential backoff prevents overwhelming the server
- Configurable retry attempts and delays

### 4. Memory Management

**New Feature:**
```python
async def cleanup_memory(self):
    import gc
    gc.collect()
    logger.debug("Memory cleanup completed")
```

**Benefits:**
- Forces garbage collection between batches
- Prevents memory accumulation during long-running operations
- Improves overall system stability

### 5. Inter-Batch Delays

**New Feature:**
```python
# Add delay and cleanup between batches
if batch_num < total_batches - 1:
    logger.info("⏳ Waiting 2 seconds before next batch...")
    await asyncio.sleep(2)
    await self.cleanup_memory()
```

**Benefits:**
- Prevents overwhelming the Qdrant server
- Allows server to process previous batch completely
- Includes memory cleanup

### 6. Enhanced Logging and Progress Tracking

**Improvements:**
- Batch-level progress reporting
- Individual opportunity tracking within batches
- Success/failure counts per batch
- Overall statistics at completion

**Example Output:**
```
📦 Processing batch 1/10 (500 opportunities)
[1/5000] Processing opportunity: ABC123 (tenant: xyz)
✅ Successfully processed ABC123
📦 Batch 1/10 completed:
  ✅ Successful: 498
  ❌ Failed: 2
⏳ Waiting 2 seconds before next batch...
```

## Usage

### Updated Command Line Options

Both scripts now support the `--batch-size` parameter:

```bash
# Process all opportunities with default batch size (500)
python scripts/qdrant_manager.py add-all-opportunities

# Process with custom batch size
python scripts/qdrant_manager.py add-all-opportunities --batch-size 250

# Dry run with custom batch size
python scripts/qdrant_manager.py add-all-opportunities --batch-size 100 --dry-run

# SAM opportunities with custom batch size
python scripts/sam_qdrant_manager.py add-all-opportunities --batch-size 300
```

### Configuration

The improvements use existing configuration from `config.py`:

```python
# Qdrant Configuration
qdrant_timeout: int = Field(60, env="QDRANT_TIMEOUT")
qdrant_prefer_grpc: bool = Field(True, env="QDRANT_PREFER_GRPC")
```

## Testing

A test script is provided to verify the improvements:

```bash
python scripts/test_batch_processing.py
```

This script tests:
- Batch processing logic
- Retry mechanisms
- Connection handling
- Memory management

## Performance Impact

**Expected Improvements:**
- **Reduced Connection Errors**: Batch processing and delays prevent server overload
- **Better Memory Usage**: Regular cleanup prevents memory accumulation
- **Improved Reliability**: Retry logic handles temporary failures
- **Progress Visibility**: Better logging helps track long-running operations
- **Configurable Performance**: Adjustable batch size for different environments

## Backward Compatibility

All changes are backward compatible:
- Default batch size of 500 maintains reasonable performance
- All existing command line options continue to work
- No changes to core functionality or data processing logic

## Recommendations

1. **Production Use**: Start with default batch size (500) and adjust based on server performance
2. **Development**: Use smaller batch sizes (50-100) for faster feedback
3. **Large Datasets**: Consider batch sizes of 200-300 for very large datasets
4. **Monitoring**: Watch server logs and adjust batch size if connection errors persist
5. **Resources**: Ensure adequate server resources when processing large batches
