#!/usr/bin/env python3
"""
Test script to verify batch processing improvements
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the parent directory to the path so we can import from AIService
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from loguru import logger
from qdrant_manager import QdrantManager, OperationMode
from sam_qdrant_manager import SAMQdrantManager


async def test_qdrant_manager_batch():
    """Test the improved QdrantManager with batch processing"""
    logger.info("Testing QdrantManager batch processing improvements...")
    
    try:
        # Test with a small batch size for testing
        manager = QdrantManager(
            mode=OperationMode.ADD_ALL_OPPORTUNITIES,
            dry_run=True,  # Use dry run for testing
            batch_size=10  # Small batch size for testing
        )
        
        # Test getting all opportunities
        opportunities = await manager.get_all_opportunities()
        logger.info(f"Found {len(opportunities)} opportunities for testing")
        
        if opportunities:
            # Test batch processing logic
            batch_size = 10
            total_batches = (len(opportunities) + batch_size - 1) // batch_size
            logger.info(f"Would process {len(opportunities)} opportunities in {total_batches} batches")
            
            # Test retry logic
            logger.info("Testing retry logic...")
            try:
                await manager.retry_with_backoff(
                    lambda: asyncio.sleep(0.1),  # Simple test function
                    max_retries=2,
                    base_delay=0.1
                )
                logger.info("✅ Retry logic test passed")
            except Exception as e:
                logger.error(f"❌ Retry logic test failed: {e}")
        
        logger.info("✅ QdrantManager batch processing test completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ QdrantManager test failed: {e}")
        return False


async def test_sam_qdrant_manager_batch():
    """Test the improved SAMQdrantManager with batch processing"""
    logger.info("Testing SAMQdrantManager batch processing improvements...")
    
    try:
        # Test with a small batch size for testing
        manager = SAMQdrantManager(
            mode=OperationMode.ADD_ALL_OPPORTUNITIES,
            dry_run=True,  # Use dry run for testing
            batch_size=10  # Small batch size for testing
        )
        
        # Test getting all SAM opportunities
        opportunities = await manager.get_all_sam_opportunities()
        logger.info(f"Found {len(opportunities)} SAM opportunities for testing")
        
        if opportunities:
            # Test batch processing logic
            batch_size = 10
            total_batches = (len(opportunities) + batch_size - 1) // batch_size
            logger.info(f"Would process {len(opportunities)} SAM opportunities in {total_batches} batches")
            
            # Test retry logic
            logger.info("Testing retry logic...")
            try:
                await manager.retry_with_backoff(
                    lambda: asyncio.sleep(0.1),  # Simple test function
                    max_retries=2,
                    base_delay=0.1
                )
                logger.info("✅ Retry logic test passed")
            except Exception as e:
                logger.error(f"❌ Retry logic test failed: {e}")
        
        logger.info("✅ SAMQdrantManager batch processing test completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ SAMQdrantManager test failed: {e}")
        return False


async def main():
    """Main test function"""
    logger.info("="*60)
    logger.info("BATCH PROCESSING IMPROVEMENTS TEST")
    logger.info("="*60)
    logger.info(f"Test started at: {datetime.now()}")
    
    # Test both managers
    qdrant_test_passed = await test_qdrant_manager_batch()
    sam_test_passed = await test_sam_qdrant_manager_batch()
    
    logger.info("="*60)
    logger.info("TEST RESULTS")
    logger.info("="*60)
    logger.info(f"QdrantManager test: {'✅ PASSED' if qdrant_test_passed else '❌ FAILED'}")
    logger.info(f"SAMQdrantManager test: {'✅ PASSED' if sam_test_passed else '❌ FAILED'}")
    
    if qdrant_test_passed and sam_test_passed:
        logger.info("🎉 All tests passed! Batch processing improvements are working correctly.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        sys.exit(1)
