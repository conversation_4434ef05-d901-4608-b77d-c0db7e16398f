#!/usr/bin/env python3
"""
SAM Qdrant Manager

A comprehensive tool for managing Qdrant collections for SAM opportunities from the OpportunityTableInfo table.
This script handles data from the kontratar_main.oppstableinfo table where:
- opportunity_id is in the opps_id field
- text data is in the opps_raw_text field (potentially encrypted)
- No tenant_id is required

Usage Examples:
    # Delete all SAM collections
    python scripts/sam_qdrant_manager.py delete-all [--dry-run]

    # Delete collections for specific SAM opportunity
    python scripts/sam_qdrant_manager.py delete-opportunity --opportunity-id 12345abcde [--dry-run]

    # Add embeddings for specific SAM opportunity
    python scripts/sam_qdrant_manager.py add-opportunity --opportunity-id 12345abcde [--dry-run]

    # Add embeddings for ALL SAM opportunities in the database
    python scripts/sam_qdrant_manager.py add-all-opportunities [--dry-run]

    # Inspect all SAM collections in Qdrant
    python scripts/sam_qdrant_manager.py inspect-collections

    # Inspect a specific SAM opportunity's collection
    python scripts/sam_qdrant_manager.py inspect-opportunity --opportunity-id 12345abcde

    # Query a specific SAM opportunity's collection
    python scripts/sam_qdrant_manager.py query-opportunity --opportunity-id 12345abcde --query "technical requirements" [--limit 10]
"""

import asyncio
import argparse
import sys
import os
from typing import List, Dict, Optional
from datetime import datetime
from enum import Enum

# Add the parent directory to the path so we can import from AIService
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger
from qdrant_client import QdrantClient

from config import settings
from database import get_kontratar_db
from models.kontratar_models import OpportunityTableInfo
from controllers.kontratar.opportunity_table_info_controller import OpportunityTableInfoController
from services.data_load.process_document import DocumentProcessingService


class OperationMode(Enum):
    DELETE_ALL = "delete_all"
    DELETE_OPPORTUNITY = "delete_opportunity"
    ADD_OPPORTUNITY = "add_opportunity"
    ADD_ALL_OPPORTUNITIES = "add_all_opportunities"
    INSPECT_COLLECTIONS = "inspect_collections"
    INSPECT_OPPORTUNITY = "inspect_opportunity"
    QUERY_OPPORTUNITY = "query_opportunity"


class SAMQdrantManager:
    """
    SAM Qdrant Manager for handling SAM opportunities from OpportunityTableInfo table
    """
    
    def __init__(self, mode: OperationMode, opportunity_id: Optional[str] = None, 
                 dry_run: bool = False, query_text: Optional[str] = None, query_limit: int = 5):
        self.mode = mode
        self.opportunity_id = opportunity_id
        self.dry_run = dry_run
        self.query_text = query_text
        self.query_limit = query_limit
        
        # Initialize Qdrant client
        self.qdrant_client = QdrantClient(url=settings.qdrant_url)
        
        # Statistics
        self.stats = {
            "collections_deleted": 0,
            "collections_created": 0,
            "documents_processed": 0,
            "chunks_created": 0,
            "opportunities_processed": 0,
            "opportunities_failed": 0,
            "start_time": None,
            "end_time": None,
            "error": None
        }

    async def get_all_sam_opportunities(self) -> List[Dict[str, str]]:
        """Retrieve all SAM opportunity IDs from the OpportunityTableInfo table"""
        logger.info("Retrieving all SAM opportunities from OpportunityTableInfo table")
        
        opportunities = []
        try:
            async for db in get_kontratar_db():
                query = select(
                    OpportunityTableInfo.opps_id
                ).where(
                    OpportunityTableInfo.opps_id.isnot(None)
                ).distinct()
                
                result = await db.execute(query)
                rows = result.fetchall()
                
                for row in rows:
                    opportunities.append({
                        "opportunity_id": row.opps_id
                    })
                
                break  # Only process first database session
                
            logger.info(f"Found {len(opportunities)} SAM opportunities in database")
            return opportunities
            
        except Exception as e:
            logger.error(f"Error retrieving SAM opportunities from database: {e}")
            return []

    async def get_sam_opportunity_data(self) -> Optional[str]:
        """Retrieve raw text data for a SAM opportunity"""
        logger.info(f"Retrieving data for SAM opportunity {self.opportunity_id}")
        
        try:
            async for db in get_kontratar_db():
                record = await OpportunityTableInfoController.get_by_opps_id(self.opportunity_id, db)
                
                if not record:
                    logger.error(f"No data found for SAM opportunity {self.opportunity_id}")
                    return None
                
                if not record.opps_raw_text:
                    logger.warning(f"No raw text data found for SAM opportunity {self.opportunity_id}")
                    return None
                
                # Handle hex-encoded data (SAM data is stored as hex strings)
                raw_text = record.opps_raw_text

                if isinstance(raw_text, str):
                    # Check if data is hex-encoded (starts with \x pattern)
                    if raw_text.startswith('\\x') or self._looks_hex_encoded(raw_text):
                        logger.info(f"Data appears to be hex-encoded for opportunity {self.opportunity_id}")
                        try:
                            # Decode hex data
                            decoded_text = self._decode_hex_data(raw_text)
                            logger.info(f"Successfully decoded hex data: {len(decoded_text)} characters")
                            return decoded_text
                        except Exception as e:
                            logger.error(f"Error decoding hex data: {e}")
                            # Fall back to using raw data
                            logger.warning("Using raw data as fallback")
                            return raw_text
                    else:
                        # Data is already plain text
                        logger.info(f"Data appears to be plain text for opportunity {self.opportunity_id}")

                elif isinstance(raw_text, bytes):
                    # Convert bytes to string
                    try:
                        raw_text = raw_text.decode('utf-8', errors='ignore')
                        logger.info("Converted bytes data to string")
                    except Exception as e:
                        logger.error(f"Error decoding bytes data: {e}")
                        return None

                logger.info(f"Retrieved raw text data: {len(raw_text)} characters")
                return raw_text
                
        except Exception as e:
            logger.error(f"Error retrieving SAM opportunity data: {e}")
            return None

    def _looks_hex_encoded(self, text: str) -> bool:
        """Check if text appears to be hex-encoded"""
        if not text:
            return False

        # Remove the \x prefix if present
        clean_text = text.replace('\\x', '')

        # Check if it's all hex characters
        import re
        hex_pattern = re.compile(r'^[0-9A-Fa-f]+$')

        # Must be even length (hex pairs) and all hex characters
        return len(clean_text) % 2 == 0 and bool(hex_pattern.match(clean_text)) and len(clean_text) > 20

    def _decode_hex_data(self, hex_text: str) -> str:
        """Decode hex-encoded text data"""
        import binascii

        # Remove \x prefixes and any whitespace
        clean_hex = hex_text.replace('\\x', '').replace(' ', '').replace('\n', '').replace('\r', '')

        try:
            # Decode hex to bytes
            decoded_bytes = binascii.unhexlify(clean_hex)

            # Convert bytes to UTF-8 string
            decoded_text = decoded_bytes.decode('utf-8', errors='ignore')

            return decoded_text

        except Exception as e:
            logger.error(f"Error in hex decoding: {e}")
            raise

    async def process_and_store_sam_data(self, raw_text: str):
        """Process the raw text using DocumentProcessingService and store in Qdrant"""
        logger.info("Processing SAM raw text using DocumentProcessingService")
        
        try:
            # Step 1: Process the corpus (clean the text)
            processed_text = DocumentProcessingService.process_corpus(raw_text)
            logger.info(f"Processed text: {len(processed_text)} characters")
            
            # Step 2: Create semantic chunks
            chunks = DocumentProcessingService.semantic_chunk(
                processed_text, 
                chunk_size=1000, 
                chunk_overlap=200
            )
            logger.info(f"Created {len(chunks)} semantic chunks")
            self.stats["chunks_created"] = len(chunks)
            
            if self.dry_run:
                logger.info("DRY RUN: Would create collection and add documents")
                logger.info(f"  Collection name: {self.opportunity_id}")
                logger.info(f"  Number of chunks: {len(chunks)}")
                self.stats["collections_created"] = 1
                self.stats["documents_processed"] = len(chunks)
                return
            
            # Step 3: Add documents to Qdrant using the existing service
            # For SAM opportunities, use just the opportunity_id as collection name
            collection_name = self.opportunity_id
            result = await DocumentProcessingService.add_documents(
                collection_name=collection_name,
                documents=chunks
            )
            
            if result:
                logger.info(f"Successfully stored {len(chunks)} documents in Qdrant collection: {collection_name}")
                self.stats["collections_created"] = 1
                self.stats["documents_processed"] = len(chunks)
            else:
                logger.error("Failed to store documents in Qdrant")
                raise Exception("Document storage failed")
                
        except Exception as e:
            logger.error(f"Error processing and storing SAM data: {e}")
            raise

    async def delete_all_sam_collections(self):
        """Delete all SAM-related collections in Qdrant"""
        logger.info("Starting deletion of all SAM Qdrant collections")

        try:
            # Get all SAM opportunity IDs from database to identify their collections
            sam_opportunities = await self.get_all_sam_opportunities()
            sam_opportunity_ids = [opp["opportunity_id"] for opp in sam_opportunities]

            collections = self.qdrant_client.get_collections()
            # Find collections that match SAM opportunity IDs (including hashed versions)
            sam_collections = []

            for col in collections.collections:
                collection_name = col.name
                # Check if it's a direct match or hashed version of a SAM opportunity
                base_name = collection_name.split(".")[0]  # Remove version suffix

                # Check if this collection belongs to a SAM opportunity
                if base_name in sam_opportunity_ids:
                    sam_collections.append(collection_name)
                else:
                    # Check if it might be a hashed version by looking up in database
                    # This is more complex but ensures we don't delete wrong collections
                    pass  # For now, only delete direct matches

            logger.info(f"Found {len(sam_collections)} SAM collections to delete")

            if self.dry_run:
                logger.info("DRY RUN: Would delete the following SAM collections:")
                for collection_name in sam_collections:
                    logger.info(f"  - {collection_name}")
                self.stats["collections_deleted"] = len(sam_collections)
                return

            for collection_name in sam_collections:
                try:
                    self.qdrant_client.delete_collection(collection_name)
                    logger.info(f"Deleted collection: {collection_name}")
                    self.stats["collections_deleted"] += 1
                except Exception as e:
                    logger.error(f"Error deleting collection {collection_name}: {e}")

        except Exception as e:
            logger.error(f"Error deleting SAM collections: {e}")
            raise

    async def delete_sam_opportunity_collections(self):
        """Delete collections for a specific SAM opportunity"""
        logger.info(f"Deleting collections for SAM opportunity: {self.opportunity_id}")

        try:
            # Find collections related to this opportunity
            matching_collections = await self.find_sam_opportunity_collections()

            if not matching_collections:
                logger.warning(f"No collections found for SAM opportunity {self.opportunity_id}")
                return

            logger.info(f"Found {len(matching_collections)} collections for SAM opportunity {self.opportunity_id}")

            if self.dry_run:
                logger.info("DRY RUN: Would delete the following collections:")
                for collection_name in matching_collections:
                    logger.info(f"  - {collection_name}")
                self.stats["collections_deleted"] = len(matching_collections)
                return

            for collection_name in matching_collections:
                try:
                    self.qdrant_client.delete_collection(collection_name)
                    logger.info(f"Deleted collection: {collection_name}")
                    self.stats["collections_deleted"] += 1
                except Exception as e:
                    logger.error(f"Error deleting collection {collection_name}: {e}")

        except Exception as e:
            logger.error(f"Error deleting SAM opportunity collections: {e}")
            raise

    async def process_all_sam_opportunities(self):
        """Process all SAM opportunities in the database"""
        logger.info("Starting batch processing of all SAM opportunities")

        # Get all opportunities from database
        opportunities = await self.get_all_sam_opportunities()

        if not opportunities:
            logger.warning("No SAM opportunities found in database")
            return

        logger.info(f"Processing {len(opportunities)} SAM opportunities")

        successful_count = 0
        failed_count = 0

        for i, opp in enumerate(opportunities, 1):
            opportunity_id = opp["opportunity_id"]

            logger.info(f"[{i}/{len(opportunities)}] Processing SAM opportunity: {opportunity_id}")

            try:
                # Temporarily set the current opportunity
                original_opportunity_id = self.opportunity_id
                self.opportunity_id = opportunity_id

                # Process this opportunity
                raw_text = await self.get_sam_opportunity_data()
                if raw_text:
                    await self.process_and_store_sam_data(raw_text)
                else:
                    raise Exception(f"No data found for SAM opportunity {opportunity_id}")

                successful_count += 1
                logger.info(f"✅ Successfully processed {opportunity_id}")

                # Restore original value
                self.opportunity_id = original_opportunity_id

            except Exception as e:
                failed_count += 1
                logger.error(f"❌ Failed to process {opportunity_id}: {e}")
                continue

        # Update statistics
        self.stats["opportunities_processed"] = successful_count
        self.stats["opportunities_failed"] = failed_count

        logger.info(f"Batch processing completed:")
        logger.info(f"  ✅ Successful: {successful_count}")
        logger.info(f"  ❌ Failed: {failed_count}")
        logger.info(f"  📊 Total: {len(opportunities)}")

    async def inspect_all_sam_collections(self):
        """Inspect all SAM collections in Qdrant with detailed information"""
        logger.info("Inspecting all SAM collections in Qdrant")

        try:
            # Get all SAM opportunity IDs from database
            sam_opportunities = await self.get_all_sam_opportunities()
            sam_opportunity_ids = [opp["opportunity_id"] for opp in sam_opportunities]

            collections = self.qdrant_client.get_collections()
            sam_collections = []

            # Find collections that match SAM opportunity IDs
            for col in collections.collections:
                collection_name = col.name
                base_name = collection_name.split(".")[0]  # Remove version suffix

                # Check if this collection belongs to a SAM opportunity
                if base_name in sam_opportunity_ids:
                    sam_collections.append(col)

            if not sam_collections:
                logger.info("No SAM collections found in Qdrant")
                return

            logger.info(f"Found {len(sam_collections)} SAM collections:")
            logger.info("="*80)

            for i, collection in enumerate(sam_collections, 1):
                collection_name = collection.name
                logger.info(f"[{i}] SAM Collection: {collection_name}")

                try:
                    # Get collection info
                    collection_info = self.qdrant_client.get_collection(collection_name)
                    vectors_count = collection_info.vectors_count if collection_info.vectors_count is not None else 0
                    status = collection_info.status.value if collection_info.status else "unknown"

                    # Get collection config
                    config = collection_info.config
                    vector_size = config.params.vectors.size if config and config.params and config.params.vectors else "unknown"
                    distance = config.params.vectors.distance.value if config and config.params and config.params.vectors and config.params.vectors.distance else "unknown"

                    logger.info(f"    📊 Vectors: {vectors_count}")
                    logger.info(f"    📏 Vector Size: {vector_size}")
                    logger.info(f"    📐 Distance Metric: {distance}")
                    logger.info(f"    🔄 Status: {status}")

                    # Try to get a sample point to see payload structure
                    try:
                        sample_points = self.qdrant_client.scroll(
                            collection_name=collection_name,
                            limit=1,
                            with_payload=True
                        )

                        if sample_points[0]:  # points
                            sample_point = sample_points[0][0]
                            payload_keys = list(sample_point.payload.keys()) if sample_point.payload else []
                            logger.info(f"    🏷️  Payload Keys: {payload_keys}")

                            # Check for opportunity info
                            if 'opps_id' in payload_keys:
                                logger.info(f"    🎯 Opportunity ID: {sample_point.payload.get('opps_id', 'N/A')}")
                            if 'source' in payload_keys:
                                logger.info(f"    📂 Source: {sample_point.payload.get('source', 'N/A')}")
                        else:
                            logger.info(f"    🏷️  No sample data available")

                    except Exception as e:
                        logger.warning(f"    ⚠️  Could not retrieve sample data: {e}")

                except Exception as e:
                    logger.error(f"    ❌ Error inspecting collection {collection_name}: {e}")

                logger.info("-" * 60)

        except Exception as e:
            logger.error(f"Error inspecting SAM collections: {e}")

    async def inspect_sam_opportunity_collection(self):
        """Inspect a specific SAM opportunity's collection in detail"""
        logger.info(f"Inspecting collection for SAM opportunity: {self.opportunity_id}")

        # Find collections related to this opportunity
        matching_collections = await self.find_sam_opportunity_collections()

        if not matching_collections:
            logger.warning(f"No collections found for SAM opportunity {self.opportunity_id}")
            return

        logger.info(f"Found {len(matching_collections)} collections for SAM opportunity {self.opportunity_id}:")
        logger.info("="*80)

        for collection_name in matching_collections:
            logger.info(f"📦 SAM Collection: {collection_name}")

            try:
                # Get detailed collection info
                collection_info = self.qdrant_client.get_collection(collection_name)
                vectors_count = collection_info.vectors_count

                logger.info(f"    📊 Total Vectors: {vectors_count}")

                # Get all points with payload
                all_points = []
                offset = None

                while True:
                    scroll_result = self.qdrant_client.scroll(
                        collection_name=collection_name,
                        limit=100,
                        offset=offset,
                        with_payload=True,
                        with_vectors=False  # Don't need vectors for inspection
                    )

                    points, next_offset = scroll_result
                    all_points.extend(points)

                    if next_offset is None:
                        break
                    offset = next_offset

                logger.info(f"    📄 Retrieved {len(all_points)} points for analysis")

                if all_points:
                    # Analyze chunks
                    chunk_indices = []
                    document_lengths = []
                    sources = set()

                    for point in all_points:
                        payload = point.payload

                        # Collect chunk indices
                        chunk_idx = payload.get('chunk_index', payload.get('document_index'))
                        if chunk_idx is not None:
                            chunk_indices.append(chunk_idx)

                        # Collect document lengths
                        doc = payload.get('document', payload.get('text', ''))
                        if doc:
                            document_lengths.append(len(doc))

                        # Collect sources
                        source = payload.get('source', 'unknown')
                        sources.add(source)

                    # Statistics
                    if chunk_indices:
                        logger.info(f"    🔢 Chunk Range: {min(chunk_indices)} - {max(chunk_indices)}")
                        logger.info(f"    📝 Total Chunks: {len(chunk_indices)}")

                    if document_lengths:
                        avg_length = sum(document_lengths) / len(document_lengths)
                        logger.info(f"    📏 Avg Chunk Length: {avg_length:.0f} characters")
                        logger.info(f"    📏 Min/Max Length: {min(document_lengths)}/{max(document_lengths)}")

                    logger.info(f"    📂 Sources: {list(sources)}")

                    # Show sample payload
                    sample_payload = all_points[0].payload
                    logger.info(f"    🏷️  Sample Payload Keys: {list(sample_payload.keys())}")

                    # Show opportunity info
                    if 'opps_id' in sample_payload:
                        logger.info(f"    🎯 Opportunity ID: {sample_payload.get('opps_id')}")

            except Exception as e:
                logger.error(f"    ❌ Error inspecting collection {collection_name}: {e}")

            logger.info("-" * 60)

    async def find_sam_opportunity_collections(self) -> List[str]:
        """Find all collections related to a SAM opportunity"""
        collections = self.qdrant_client.get_collections()
        collection_names = [col.name for col in collections.collections]

        matching_collections = []

        # Check for direct name match (opportunity_id as collection name)
        for name in collection_names:
            base_name = name.split(".")[0]  # Remove version suffix
            if base_name == self.opportunity_id:
                matching_collections.append(name)

        # Also check for hashed versions by looking up in ChromaDB mapping
        # This would require database access to find the hashed collection name
        try:
            async for db in get_kontratar_db():
                from controllers.kontratar.chromadb_mapping_controller import ChromaDBMappingController
                controller = ChromaDBMappingController()

                # For SAM opportunities, check both "sam" and "SYSTEM" tenant_ids
                for tenant_id in ["sam", "SYSTEM"]:
                    mapping = None
                    if self.opportunity_id:
                        mapping = await controller.get_by_unique_id_and_tenant(db, self.opportunity_id, tenant_id)
                    if mapping and mapping.collection_name:
                        hashed_name = mapping.collection_name
                        logger.debug(f"Found hashed collection name: {hashed_name} for tenant: {tenant_id}")
                        # Check for this hashed name and its versions
                        for name in collection_names:
                            base_name = name.split(".")[0]
                            if base_name == hashed_name:
                                matching_collections.append(name)
                                logger.info(f"Found matching collection: {name}")
                break
        except Exception as e:
            logger.debug(f"Could not check for hashed collection names: {e}")

        # Remove duplicates
        return list(set(matching_collections))

    async def query_sam_opportunity_collection(self, query_text: str, limit: int = 5):
        """Query a specific SAM opportunity's collection"""
        logger.info(f"Querying SAM opportunity {self.opportunity_id} with: '{query_text}'")

        # Find collections for this opportunity
        matching_collections = await self.find_sam_opportunity_collections()

        if not matching_collections:
            logger.warning(f"No collections found for SAM opportunity {self.opportunity_id}")
            return

        # Use the first matching collection (or could query all)
        collection_name = matching_collections[0]
        logger.info(f"Using collection: {collection_name}")

        try:
            # Generate query embedding
            from utils.embedding_model import KontratarEmbeddings
            embeddings = KontratarEmbeddings("http://ai.kontratar.com:5000")
            query_embedding = embeddings.embed_query(query_text)

            logger.info(f"Generated query embedding with {len(query_embedding)} dimensions")

            # Search in Qdrant
            search_results = self.qdrant_client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                limit=limit,
                with_payload=True,
                score_threshold=0.0  # Include all results
            )

            if not search_results:
                logger.warning("No results found for the query")
                return

            logger.info(f"Found {len(search_results)} results:")
            logger.info("="*80)

            for i, result in enumerate(search_results, 1):
                score = result.score
                payload = result.payload
                document = payload.get('document', payload.get('text', 'No document text'))
                chunk_idx = payload.get('chunk_index', payload.get('document_index', 'N/A'))

                logger.info(f"[{i}] Score: {score:.4f} | Chunk: {chunk_idx}")

                # Show document preview (first 200 chars)
                preview = document[:200] + "..." if len(document) > 200 else document
                logger.info(f"    📄 Content: {preview}")

                # Show other metadata
                for key, value in payload.items():
                    if key not in ['document', 'text'] and value is not None:
                        logger.info(f"    🏷️  {key}: {value}")

                logger.info("-" * 60)

        except Exception as e:
            logger.error(f"Error querying SAM collection: {e}")

    async def run(self):
        """Main execution method"""
        self.stats["start_time"] = datetime.now()

        try:
            logger.info("="*60)
            logger.info(f"Qdrant Url: {settings.qdrant_url}")
            logger.info(f"SAM QDRANT MANAGER - {self.mode.value.upper()}")
            logger.info("="*60)
            logger.info(f"Mode: {self.mode.value}")
            if self.opportunity_id:
                logger.info(f"Opportunity ID: {self.opportunity_id}")
            logger.info(f"Dry Run: {self.dry_run}")
            logger.info("="*60)

            if self.mode == OperationMode.DELETE_ALL:
                await self.delete_all_sam_collections()

            elif self.mode == OperationMode.DELETE_OPPORTUNITY:
                await self.delete_sam_opportunity_collections()

            elif self.mode == OperationMode.ADD_OPPORTUNITY:
                # Get opportunity data
                raw_text = await self.get_sam_opportunity_data()
                if not raw_text:
                    raise Exception("No raw text data found for SAM opportunity")

                # Process and store data
                await self.process_and_store_sam_data(raw_text)

            elif self.mode == OperationMode.ADD_ALL_OPPORTUNITIES:
                # Process all opportunities in the database
                await self.process_all_sam_opportunities()

            elif self.mode == OperationMode.INSPECT_COLLECTIONS:
                # Inspect all collections in Qdrant
                await self.inspect_all_sam_collections()

            elif self.mode == OperationMode.INSPECT_OPPORTUNITY:
                # Inspect specific opportunity collection
                await self.inspect_sam_opportunity_collection()

            elif self.mode == OperationMode.QUERY_OPPORTUNITY:
                # Query specific opportunity collection
                query_text = getattr(self, 'query_text', 'default query')
                limit = getattr(self, 'query_limit', 5)
                await self.query_sam_opportunity_collection(query_text, limit)

            self.stats["end_time"] = datetime.now()
            duration = self.stats["end_time"] - self.stats["start_time"]

            logger.info("="*60)
            logger.info("EXECUTION COMPLETED SUCCESSFULLY")
            logger.info("="*60)
            logger.info(f"Mode: {self.mode.value}")
            logger.info(f"Collections deleted: {self.stats['collections_deleted']}")
            logger.info(f"Collections created: {self.stats['collections_created']}")
            logger.info(f"Documents processed: {self.stats['documents_processed']}")
            logger.info(f"Chunks created: {self.stats['chunks_created']}")
            if self.mode == OperationMode.ADD_ALL_OPPORTUNITIES:
                logger.info(f"Opportunities processed: {self.stats['opportunities_processed']}")
                logger.info(f"Opportunities failed: {self.stats['opportunities_failed']}")
            logger.info(f"Duration: {duration}")
            logger.info("="*60)

        except Exception as e:
            self.stats["error"] = str(e)
            self.stats["end_time"] = datetime.now()
            duration = self.stats["end_time"] - self.stats["start_time"]

            logger.error("="*60)
            logger.error("EXECUTION FAILED")
            logger.error("="*60)
            logger.error(f"Error: {e}")
            logger.error(f"Duration: {duration}")
            logger.error("="*60)
            raise


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="SAM Qdrant Collection Manager",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Delete all SAM collections
  python scripts/sam_qdrant_manager.py delete-all --dry-run

  # Delete collections for specific SAM opportunity
  python scripts/sam_qdrant_manager.py delete-opportunity --opportunity-id 12345abcde

  # Add embeddings for specific SAM opportunity
  python scripts/sam_qdrant_manager.py add-opportunity --opportunity-id 12345abcde

  # Add embeddings for ALL SAM opportunities in the database
  python scripts/sam_qdrant_manager.py add-all-opportunities --dry-run

  # Inspect all SAM collections in Qdrant
  python scripts/sam_qdrant_manager.py inspect-collections

  # Inspect a specific SAM opportunity's collection
  python scripts/sam_qdrant_manager.py inspect-opportunity --opportunity-id 12345abcde

  # Query a specific SAM opportunity's collection
  python scripts/sam_qdrant_manager.py query-opportunity --opportunity-id 12345abcde --query "technical requirements"
        """
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Delete all command
    delete_all_parser = subparsers.add_parser("delete-all", help="Delete all SAM collections in Qdrant")
    delete_all_parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")

    # Delete opportunity command
    delete_opp_parser = subparsers.add_parser("delete-opportunity", help="Delete collections for a specific SAM opportunity")
    delete_opp_parser.add_argument("--opportunity-id", required=True, help="SAM Opportunity ID to delete")
    delete_opp_parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")

    # Add opportunity command
    add_opp_parser = subparsers.add_parser("add-opportunity", help="Add embeddings for a specific SAM opportunity")
    add_opp_parser.add_argument("--opportunity-id", required=True, help="SAM Opportunity ID to process")
    add_opp_parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")

    # Add all opportunities command
    add_all_parser = subparsers.add_parser("add-all-opportunities", help="Add embeddings for all SAM opportunities in the database")
    add_all_parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")

    # Inspect collections command
    inspect_parser = subparsers.add_parser("inspect-collections", help="Inspect all SAM collections in Qdrant")

    # Inspect opportunity command
    inspect_opp_parser = subparsers.add_parser("inspect-opportunity", help="Inspect a specific SAM opportunity's collection")
    inspect_opp_parser.add_argument("--opportunity-id", required=True, help="SAM Opportunity ID to inspect")

    # Query opportunity command
    query_parser = subparsers.add_parser("query-opportunity", help="Query a specific SAM opportunity's collection")
    query_parser.add_argument("--opportunity-id", required=True, help="SAM Opportunity ID to query")
    query_parser.add_argument("--query", required=True, help="Query text to search for")
    query_parser.add_argument("--limit", type=int, default=5, help="Number of results to return (default: 5)")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    try:
        # Determine operation mode
        if args.command == "delete-all":
            mode = OperationMode.DELETE_ALL
            opportunity_id = None
        elif args.command == "delete-opportunity":
            mode = OperationMode.DELETE_OPPORTUNITY
            opportunity_id = args.opportunity_id
        elif args.command == "add-opportunity":
            mode = OperationMode.ADD_OPPORTUNITY
            opportunity_id = args.opportunity_id
        elif args.command == "add-all-opportunities":
            mode = OperationMode.ADD_ALL_OPPORTUNITIES
            opportunity_id = None
        elif args.command == "inspect-collections":
            mode = OperationMode.INSPECT_COLLECTIONS
            opportunity_id = None
        elif args.command == "inspect-opportunity":
            mode = OperationMode.INSPECT_OPPORTUNITY
            opportunity_id = args.opportunity_id
        elif args.command == "query-opportunity":
            mode = OperationMode.QUERY_OPPORTUNITY
            opportunity_id = args.opportunity_id
        else:
            logger.error(f"Unknown command: {args.command}")
            sys.exit(1)

        # Get query parameters if this is a query command
        query_text = getattr(args, 'query', None)
        query_limit = getattr(args, 'limit', 5)
        dry_run = getattr(args, 'dry_run', False)

        manager = SAMQdrantManager(
            mode=mode,
            opportunity_id=opportunity_id,
            dry_run=dry_run,
            query_text=query_text,
            query_limit=query_limit
        )

        asyncio.run(manager.run())

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
